{"name": "uglify-js", "description": "JavaScript parser, mangler/compressor and beautifier toolkit", "author": "<PERSON><PERSON> <<EMAIL>> (http://lisperator.net/)", "license": "BSD-2-<PERSON><PERSON>", "version": "3.19.3", "engines": {"node": ">=0.8.0"}, "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>> (http://lisperator.net/)"], "repository": "mishoo/UglifyJS", "main": "tools/node.js", "bin": {"uglifyjs": "bin/uglifyjs"}, "files": ["bin", "lib", "tools", "LICENSE"], "devDependencies": {"acorn": "~8.7.1", "semver": "~6.3.0"}, "scripts": {"test": "node test/compress.js && node test/mocha.js"}, "keywords": ["cli", "compress", "compressor", "ecma", "ecmascript", "es", "es5", "javascript", "js", "jsmin", "min", "minification", "minifier", "minify", "optimize", "optimizer", "pack", "packer", "parse", "parser", "uglifier", "uglify"]}