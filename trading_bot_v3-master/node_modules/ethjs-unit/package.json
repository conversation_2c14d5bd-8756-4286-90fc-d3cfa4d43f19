{"name": "ethjs-unit", "version": "0.1.6", "description": "A simple module for handling Ethereum units (e.g. 'ether', 'wei', etc...)", "main": "lib/index.js", "files": ["dist", "internals", "lib", "src"], "scripts": {"start": "npm test", "release": "npmpub", "pretest": "npm run lint", "prepublish": "npm run build", "prebuild": "npm run build:clean && npm run test", "build:clean": "npm run test:clean && rimraf ./dist", "build:commonjs": "cross-env BABEL_ENV=commonjs babel src --out-dir lib --copy-files", "build:umd": "cross-env BABEL_ENV=commonjs NODE_ENV=development webpack --config ./internals/webpack/webpack.config.js ./lib/index.js --progress", "build:umd:min": "cross-env BABEL_ENV=commonjs NODE_ENV=production webpack --config ./internals/webpack/webpack.config.js ./lib/index.js --progress", "build": "npm run build:commonjs && npm run test:lib && npm run build:umd && npm run build:umd:min", "lint": "npm run lint:js", "lint:eslint": "eslint --ignore-path .gitignore --ignore-pattern **/**.min.js", "lint:js": "npm run lint:eslint -- . ", "lint:staged": "lint-staged", "test:clean": "rimraf ./coverage", "test": "mocha ./src/tests/**/*.js -R spec --timeout 2000000", "test:lib": "mocha ./lib/tests/**/*.js -R spec --timeout 2000000", "test-travis": "node ./node_modules/istanbul/lib/cli.js cover ./node_modules/mocha/bin/_mocha -- src/tests/**/*.js -R spec --timeout 2000000", "coveralls": "npm run test-travis && cat ./coverage/lcov.info | coveralls"}, "engines": {"npm": ">=3", "node": ">=6.5.0"}, "babel": {"plugins": [["transform-es2015-template-literals", {"loose": true}], "transform-es2015-literals", "transform-es2015-function-name", "transform-es2015-arrow-functions", "transform-es2015-block-scoped-functions", ["transform-es2015-classes", {"loose": true}], "transform-es2015-object-super", "transform-es2015-shorthand-properties", ["transform-es2015-computed-properties", {"loose": true}], ["transform-es2015-for-of", {"loose": true}], "transform-es2015-sticky-regex", "transform-es2015-unicode-regex", "check-es2015-constants", ["transform-es2015-spread", {"loose": true}], "transform-es2015-parameters", ["transform-es2015-destructuring", {"loose": true}], "transform-es2015-block-scoping", "transform-object-rest-spread", "transform-es3-member-expression-literals", "transform-es3-property-literals"], "env": {"commonjs": {"plugins": [["transform-es2015-modules-commonjs", {"loose": true}]]}}}, "dependencies": {"bn.js": "4.11.6", "number-to-bn": "1.7.0"}, "devDependencies": {"bignumber.js": "3.0.1", "babel-cli": "6.18.0", "babel-core": "6.18.2", "babel-loader": "6.2.8", "babel-plugin-check-es2015-constants": "6.8.0", "babel-plugin-transform-es2015-arrow-functions": "6.8.0", "babel-plugin-transform-es2015-block-scoped-functions": "6.8.0", "babel-plugin-transform-es2015-block-scoping": "6.18.0", "babel-plugin-transform-es2015-classes": "6.18.0", "babel-plugin-transform-es2015-computed-properties": "6.8.0", "babel-plugin-transform-es2015-destructuring": "6.19.0", "babel-plugin-transform-es2015-for-of": "6.18.0", "babel-plugin-transform-es2015-function-name": "6.9.0", "babel-plugin-transform-es2015-literals": "6.8.0", "babel-plugin-transform-es2015-modules-commonjs": "6.18.0", "babel-plugin-transform-es2015-object-super": "6.8.0", "babel-plugin-transform-es2015-parameters": "6.18.0", "babel-plugin-transform-es2015-shorthand-properties": "6.18.0", "babel-plugin-transform-es2015-spread": "6.8.0", "babel-plugin-transform-es2015-sticky-regex": "6.8.0", "babel-plugin-transform-es2015-template-literals": "6.8.0", "babel-plugin-transform-es2015-unicode-regex": "6.11.0", "babel-plugin-transform-es3-member-expression-literals": "6.5.0", "babel-plugin-transform-es3-property-literals": "6.5.0", "babel-plugin-transform-object-rest-spread": "6.19.0", "babel-register": "6.18.0", "check-es3-syntax-cli": "0.1.3", "webpack": "2.1.0-beta.15", "json-loader": "0.5.4", "rimraf": "2.3.4", "cross-env": "1.0.7", "babel-eslint": "7.1.0", "chai": "3.5.0", "coveralls": "2.11.9", "eslint": "2.10.1", "istanbul": "0.4.5", "eslint-config-airbnb": "9.0.1", "eslint-import-resolver-webpack": "0.2.4", "eslint-plugin-import": "1.8.0", "eslint-plugin-jsx-a11y": "1.2.0", "eslint-plugin-react": "5.1.1", "eventsource-polyfill": "0.9.6", "lint-staged": "1.0.1", "mocha": "3.1.2", "pre-commit": "1.1.3", "web3": "0.17.0-beta"}, "keywords": ["ethereum", "encoding", "decoding"], "author": "<PERSON> <then<PERSON><PERSON><PERSON>@gmail.com>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://ethers.io"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/debris"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://frozeman.de"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/cubedro"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://gavwood.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/obscuren"}], "repository": {"type": "git", "url": "git://github.com/ethjs/ethjs-unit"}, "license": "MIT", "lint-staged": {"lint:eslint": "*.js"}, "eslintConfig": {"parser": "babel-es<PERSON>", "extends": "airbnb", "env": {"node": true, "mocha": true, "es6": true}, "parserOptions": {"ecmaVersion": 6, "sourceType": "module"}, "rules": {"import/no-unresolved": 2, "comma-dangle": [2, "always-multiline"], "indent": [2, 2, {"SwitchCase": 1}], "no-console": 1, "max-len": 0, "prefer-template": 2, "no-use-before-define": 0, "newline-per-chained-call": 0, "arrow-body-style": [2, "as-needed"]}}, "pre-commit": "build"}