{"version": 3, "file": "rlp.js", "sourceRoot": "", "sources": ["src/rlp.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,KAAK,CAAC,MAAM,cAAc,CAAC;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAIrC,2DAA2D;AAC3D,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC;IACvB,YAAY,CAAC,CAAW,EAAE,KAAa;QACrC,IAAI,KAAK,GAAG,EAAE;YAAE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjB,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,OAAO,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE;YAAE,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBAAE,MAAM;QAChE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7B,CAAC;IACD,YAAY,CAAC,CAAW;QACtB,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,KAAK,KAAK,KAAK;YAAE,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrD,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,IAAI,GAAG,CAAC,CAAC,CAAC;gBAAE,MAAM;YAClB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QACD,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,GAAG,IAAI,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACpF,OAAO,GAAG,CAAC;IACb,CAAC;CACF,CAAC,CAAC;AAUH,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE;IAChE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACf,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;QACvD,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;QAC1B,IAAI,EAAE,CAAC,CAAC,MAAM,CACZ,SAAS,EACT,CAAC,CAAC,KAAK,CACL,IAAI,EACJ,CAAC,CAAC,IAAI,CAAC,GAA6B,EAAE,CAAC,QAAQ,CAAC,CACjD,CACF;KACF,CAAC;CACH,CAAC,CAAC;AAEH,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACzB,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5B,MAAM,KAAK,GAAG,UAAU,CAAC,EAAE,EAAE,CAAC;AAE9B;;;;GAIG;AACH,MAAM,CAAC,MAAM,GAAG,GAA0B,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE;IAC1D,MAAM,CAAC,IAAiB;QACtB,IAAI,IAAI,CAAC,GAAG,KAAK,MAAM;YAAE,OAAO,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,OAAO,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;gBACpD,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;YAC3F,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB,CAAC;QACD,IAAI,OAAO,CAAC,GAAG,KAAK,MAAM;YAAE,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACD,MAAM,CAAC,IAAc;QACnB,IAAI,IAAI,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5C,QAAQ,OAAO,IAAI,EAAE,CAAC;YACpB,KAAK,QAAQ;gBACX,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;oBAClB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBACrB,IAAI,IAAI,GAAG,GAAG;4BAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;oBACrD,CAAC;oBACD,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;gBACjE,CAAC;gBACD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;oBACrB,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC1F,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,KAAK,QAAQ;gBACX,IAAI,IAAI,GAAG,CAAC;oBAAE,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;gBAC3F,IAAI,IAAI,KAAK,CAAC;oBAAE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/C,KAAK,QAAQ;gBACX,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;oBAClB,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;gBAC/E,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/C,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YACpF;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}