{"version": 3, "file": "ssz.d.ts", "sourceRoot": "", "sources": ["src/ssz.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,CAAC,MAAM,cAAc,CAAC;AA4BlC,eAAO,MAAM,SAAS;;;;;;CAMZ,CAAC;AAEX,MAAM,MAAM,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG;IACzC,OAAO,EAAE,CAAC,CAAC;IACX,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM,CAAA;KAAE,CAAC;IAEvB,SAAS,EAAE,OAAO,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IAEnB,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,UAAU,EAAE,CAAC;IACnC,UAAU,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,UAAU,CAAC;IACrC,eAAe,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC;CACpD,CAAC;AA2JF,eAAO,MAAM,KAAK,2BAA4B,CAAC;AAC/C,eAAO,MAAM,MAAM,2BAA6B,CAAC;AACjD,eAAO,MAAM,MAAM,2BAA6B,CAAC;AACjD,eAAO,MAAM,MAAM,2BAAsC,CAAC;AAC1D,eAAO,MAAM,OAAO,2BAAwC,CAAC;AAC7D,eAAO,MAAM,OAAO,2BAAwC,CAAC;AAC7D,eAAO,MAAM,OAAO,mBAAkC,CAAC;AAmCvD,KAAK,UAAU,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG;IAAE,IAAI,EAAE;QAAE,IAAI,EAAE,QAAQ,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAA;KAAE,CAAA;CAAE,CAAC;AACjG;;GAEG;AACH,eAAO,MAAM,MAAM,GAAI,CAAC,EAAE,KAAK,MAAM,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,KAAG,UAAU,CAAC,CAAC,CAoBvE,CAAC;AACF,KAAK,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG;IAAE,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAA;KAAE,CAAA;CAAE,CAAC;AAC7F;;GAEG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,QAAQ,MAAM,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,KAAG,QAAQ,CAAC,CAAC,CAwBtE,CAAC;AA4BF,KAAK,cAAc,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC;KACrE,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACpC,CAAC,GAAG;IAAE,IAAI,EAAE;QAAE,IAAI,EAAE,WAAW,CAAC;QAAC,MAAM,EAAE,CAAC,CAAA;KAAE,CAAA;CAAE,CAAC;AAEhD;;GAEG;AACH,eAAO,MAAM,SAAS,GAAI,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,EAC/D,QAAQ,CAAC,KACR,cAAc,CAAC,CAAC,CAgClB,CAAC;AAkBF,KAAK,aAAa,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAG;IAAE,IAAI,EAAE;QAAE,IAAI,EAAE,WAAW,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAA;CAAE,CAAC;AACtF;;GAEG;AACH,eAAO,MAAM,SAAS,GAAI,KAAK,MAAM,KAAG,aAqBvC,CAAC;AACF,KAAK,WAAW,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAG;IAAE,IAAI,EAAE;QAAE,IAAI,EAAE,SAAS,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAA;CAAE,CAAC;AAClF;;GAEG;AACH,eAAO,MAAM,OAAO,GAAI,QAAQ,MAAM,KAAG,WAwCxC,CAAC;AAEF;;KAEK;AACL,eAAO,MAAM,KAAK,GAChB,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,KACjC,QAAQ,CAAC;IAAE,QAAQ,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,GAAG,CAAA;CAAE,CAyC3C,CAAC;AACF,KAAK,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG;IACzC,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,OAAO,IAAI,CAAA;KAAE,CAAC;CACvD,CAAC;AACF;;GAEG;AACH,eAAO,MAAM,QAAQ,GAAI,QAAQ,MAAM,KAAG,YAsBzC,CAAC;AACF,KAAK,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG;IAC3C,IAAI,EAAE;QAAE,IAAI,EAAE,QAAQ,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,OAAO,IAAI,CAAA;KAAE,CAAC;CACzD,CAAC;AACF;;GAEG;AACH,eAAO,MAAM,UAAU,GAAI,KAAK,MAAM,KAAG,cAmBxC,CAAC;AAEF,KAAK,oBAAoB,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC;KAC3E,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACrC,CAAC,GAAG;IAAE,IAAI,EAAE;QAAE,IAAI,EAAE,iBAAiB,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,CAAC,CAAA;KAAE,CAAA;CAAE,CAAC;AACjE;;GAEG;AACH,eAAO,MAAM,eAAe,GAAI,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,EACrE,GAAG,MAAM,EACT,QAAQ,CAAC,KACR,oBAAoB,CAAC,CAAC,CAuDxB,CAAC;AAEF,KAAK,YAAY,CACf,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,EACvC,IAAI,SAAS,MAAM,CAAC,GAAG,MAAM,EAC7B,IAAI,SAAS,MAAM,CAAC,GAAG,MAAM,IAC3B,QAAQ,CAAC;KAAG,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GAAG;KAAG,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,CAAC,GAAG;IAC3F,IAAI,EAAE;QAAE,IAAI,EAAE,SAAS,CAAC;QAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAA;KAAE,CAAC;CAC/D,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,eAAO,MAAM,OAAO,GAClB,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,EACvC,IAAI,SAAS,MAAM,CAAC,GAAG,MAAM,EAC7B,IAAI,SAAS,MAAM,CAAC,GAAG,MAAM,EAE7B,GAAG,oBAAoB,CAAC,CAAC,CAAC,EAC1B,WAAW,IAAI,EAAE,EACjB,iBAAgB,IAAI,EAAO,EAC3B,cAAa,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,KACpC,YAAY,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAkG5B,CAAC;AAGF,eAAO,MAAM,IAAI,2BAAQ,CAAC;AAC1B,eAAO,MAAM,GAAG,mBAAU,CAAC;AAC3B,eAAO,MAAM,IAAI,mBAAU,CAAC;AAC5B,eAAO,MAAM,KAAK,QA/Oc,MAAM,KAAG,cA+OV,CAAC;AAoYhC,eAAO,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgFtB,CAAC;AAiIF,eAAO,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAS1B,CAAC;AAsEF,eAAO,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgFzB,CAAC;AAEF,oBAAoB;AACpB,eAAO,MAAM,6BAA6B;;;;;;;;;;;;;;;;EAgBxC,CAAC;AAkBH,eAAO,MAAM,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAM7B,CAAC;AAEH,eAAO,MAAM,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAGnC,CAAC;AAEH,eAAO,MAAM,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6B7B,CAAC;AAEH,sBAAsB;AACtB,eAAO,MAAM,+BAA+B;;;;;;;;;;;;;;;EAe1C,CAAC;AAkBH,eAAO,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAM/B,CAAC;AAEH,eAAO,MAAM,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAGrC,CAAC;AAEH,eAAO,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0B/B,CAAC;AAcH,eAAO,MAAM,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAM5B,CAAC;AAEH,eAAO,MAAM,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAGlC,CAAC;AAEH,eAAO,MAAM,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAyB5B,CAAC;AAaH,eAAO,MAAM,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAM5B,CAAC;AAEH,eAAO,MAAM,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAGlC,CAAC;AACH,eAAO,MAAM,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsB5B,CAAC"}