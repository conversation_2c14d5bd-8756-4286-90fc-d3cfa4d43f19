{"version": 3, "file": "typed-data.d.ts", "sourceRoot": "", "sources": ["src/typed-data.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAM9D,MAAM,MAAM,GAAG,GAAG,MAAM,GAAG,UAAU,CAAC;AACtC,MAAM,WAAW,WAAW,CAAC,CAAC;IAC5B,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,MAAM,CAAC;IACjC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,OAAO,GAAG,UAAU,GAAG,MAAM,CAAC;IAC/E,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,MAAM,CAAC;IACxD,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC;CACjE;AAuDD,eAAO,MAAM,QAAQ,EAAE,WAAW,CAAC,MAAM,GAAG,UAAU,CAMrD,CAAC;AAcF,MAAM,MAAM,eAAe,GAAG;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,IAAI,EAAE,MAAM,CAAA;CAAE,CAAC;AAC7D,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,SAAS,eAAe,EAAE,CAAC,CAAC;AAMrE,KAAK,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,KAAK,SAAS,WAAW,IAC1D,CAAC,SAAS,GAAG,MAAM,IAAI,KAAK,MAAM,IAAI,EAAE,GAAG,WAAW,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,EAAE,KAAK,CAAC,EAAE,GACjF,CAAC,SAAS,GAAG,MAAM,IAAI,IAAI,MAAM,IAAI,MAAM,IAAI,EAAE,GAAG,WAAW,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,EAAE,KAAK,CAAC,EAAE,GAC1F,CAAC,SAAS,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,SAAS,GACrD,UAAU,CAAC,CAAC,CAAC,CAAC;AAEhB,MAAM,MAAM,OAAO,CAAC,KAAK,SAAS,WAAW,EAAE,CAAC,SAAS,MAAM,KAAK,GAAG,MAAM,IAAI;KAC9E,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC;CACpE,CAAC;AACF,KAAK,GAAG,CAAC,CAAC,SAAS,WAAW,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC;AAmGnD,wBAAgB,OAAO,CAAC,CAAC,SAAS,WAAW,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,cAAc,CAAC;iBA6D1E,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM;iBAE1D,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM;eAG5D,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM;WAErE,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,eACR,CAAC,WACL,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,cACV,GAAG,iBACA,OAAO,GAAG,UAAU,KAClC,MAAM;aACA,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,eACV,CAAC,aACH,MAAM,WACR,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,WACb,MAAM,KACd,OAAO;uBACS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,eACpB,CAAC,aACH,MAAM,WACR,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KACrB,MAAM;EAEZ;AAED,eAAO,MAAM,YAAY;;;;;;;;;;;;;;;EAMf,CAAC;AACX,MAAM,MAAM,YAAY,GAAG,OAAO,YAAY,CAAC;AAE/C,QAAA,MAAM,WAAW;kBAAmC,YAAY;CAAE,CAAC;AACnE,MAAM,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,WAAW,EAAE,cAAc,CAAC,CAAC;AAGvE,wBAAgB,aAAa,CAAC,MAAM,EAAE,YAAY;;;;;;;;;;;;;;;KAEjD;AAED,MAAM,MAAM,SAAS,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI;IAC/D,KAAK,EAAE,CAAC,CAAC;IACT,WAAW,EAAE,CAAC,CAAC;IACf,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;IACnC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CACxB,CAAC;AAeF,wBAAgB,UAAU,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAChE,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GACrB,MAAM,CAMR;AAED,wBAAgB,OAAO,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAM/F;AAED,wBAAgB,SAAS,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAC/D,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EACtB,UAAU,EAAE,GAAG,EACf,YAAY,CAAC,EAAE,OAAO,GAAG,UAAU,GAClC,MAAM,CAQR;AAED,wBAAgB,WAAW,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EACjE,SAAS,EAAE,MAAM,EACjB,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EACtB,OAAO,EAAE,MAAM,GACd,OAAO,CAQT;AAED,wBAAgB,qBAAqB,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAC3E,SAAS,EAAE,MAAM,EACjB,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GACrB,MAAM,CAMR;AAGD,eAAO,MAAM,KAAK,EAAE,GAAuE,CAAC"}