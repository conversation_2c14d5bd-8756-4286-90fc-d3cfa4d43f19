# lodash.camelcase v4.3.0

The [lodash](https://lodash.com/) method `_.camelCase` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.camelcase
```

In Node.js:
```js
var camelCase = require('lodash.camelcase');
```

See the [documentation](https://lodash.com/docs#camelCase) or [package source](https://github.com/lodash/lodash/blob/4.3.0-npm-packages/lodash.camelcase) for more details.
