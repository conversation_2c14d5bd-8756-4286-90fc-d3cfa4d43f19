import { WordlistOwl } from "./wordlist-owl.js";
/**
 *  The [[link-bip39-pt]] for [mnemonic phrases](link-bip-39).
 *
 *  @_docloc: api/wordlists
 */
export declare class LangPt extends WordlistOwl {
    /**
     *  Creates a new instance of the Portuguese language Wordlist.
     *
     *  This should be unnecessary most of the time as the exported
     *  [[langPt]] should suffice.
     *
     *  @_ignore:
     */
    constructor();
    /**
     *  Returns a singleton instance of a ``LangPt``, creating it
     *  if this is the first time being called.
     */
    static wordlist(): LangPt;
}
//# sourceMappingURL=lang-pt.d.ts.map