{"name": "ordinal", "version": "1.0.3", "description": "Module to provide the ordinal letters following a numeral", "main": "index.js", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "keywords": ["ordinal", "number", "series", "1st", "2nd", "3rd", "4th", "5th", "nth", "first", "second", "third"], "homepage": "https://github.com/dcousens/ordinal", "bugs": {"url": "https://github.com/dcousens/ordinal/issues"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/dcousens/ordinal.git"}, "author": "<PERSON>", "devDependencies": {"standard": "*", "tape": "^4.6.0"}}