import { ArgumentConfig, IWriteMarkDown, ParseOptions, UsageGuideConfig } from './contracts';

export const replaceBelowDefault = `[//]: ####ts-command-line-args_write-markdown_replaceBelow`;
export const replaceAboveDefault = `[//]: ####ts-command-line-args_write-markdown_replaceAbove`;
export const insertCodeBelowDefault = `[//]: # (ts-command-line-args_write-markdown_insertCodeBelow`;
export const insertCodeAboveDefault = `[//]: # (ts-command-line-args_write-markdown_insertCodeAbove)`;
export const copyCodeBelowDefault = `// ts-command-line-args_write-markdown_copyCodeBelow`;
export const copyCodeAboveDefault = `// ts-command-line-args_write-markdown_copyCodeAbove`;
export const configImportNameDefault = `usageGuideInfo`;
export const footerReplaceBelowMarker = `[//]: ####ts-command-line-args_generated-by-footer`;

export const argumentConfig: ArgumentConfig<IWriteMarkDown> = {
    markdownPath: {
        type: String,
        alias: 'm',
        defaultOption: true,
        description:
            'The file to write to. Without replacement markers the whole file content will be replaced. Path can be absolute or relative.',
    },
    replaceBelow: {
        type: String,
        defaultValue: replaceBelowDefault,
        description: `A marker in the file to replace text below.`,
        optional: true,
    },
    replaceAbove: {
        type: String,
        defaultValue: replaceAboveDefault,
        description: `A marker in the file to replace text above.`,
        optional: true,
    },
    insertCodeBelow: {
        type: String,
        defaultValue: insertCodeBelowDefault,
        description: `A marker in the file to insert code below. File path to insert must be added at the end of the line and optionally codeComment flag: 'insertToken file="path/toFile.md" codeComment="ts"'`,
        optional: true,
    },
    insertCodeAbove: {
        type: String,
        defaultValue: insertCodeAboveDefault,
        description: `A marker in the file to insert code above.`,
        optional: true,
    },
    copyCodeBelow: {
        type: String,
        defaultValue: copyCodeBelowDefault,
        description: `A marker in the file being inserted to say only copy code below this line`,
        optional: true,
    },
    copyCodeAbove: {
        type: String,
        defaultValue: copyCodeAboveDefault,
        description: `A marker in the file being inserted to say only copy code above this line`,
        optional: true,
    },
    jsFile: {
        type: String,
        optional: true,
        alias: 'j',
        description: `jsFile to 'require' that has an export with the 'UsageGuideConfig' export. Multiple files can be specified.`,
        multiple: true,
    },
    configImportName: {
        type: String,
        alias: 'c',
        defaultValue: [configImportNameDefault],
        description: `Export name of the 'UsageGuideConfig' object. Defaults to '${configImportNameDefault}'. Multiple exports can be specified.`,
        multiple: true,
    },
    verify: {
        type: Boolean,
        alias: 'v',
        description: `Verify the markdown file. Does not update the file but returns a non zero exit code if the markdown file is not correct. Useful for a pre-publish script.`,
    },
    configFile: {
        type: String,
        alias: 'f',
        optional: true,
        description: `Optional config file to load config from. package.json can be used if jsonPath specified as well`,
    },
    jsonPath: {
        type: String,
        alias: 'p',
        optional: true,
        description: `Used in conjunction with 'configFile'. The path within the config file to load the config from. For example: 'configs.writeMarkdown'`,
    },
    verifyMessage: {
        type: String,
        optional: true,
        description: `Optional message that is printed when markdown verification fails. Use '\\{fileName\\}' to refer to the file being processed.`,
    },
    removeDoubleBlankLines: {
        type: Boolean,
        description: 'When replacing content removes any more than a single blank line',
    },
    skipFooter: {
        type: Boolean,
        description: `Does not add the 'Markdown Generated by...' footer to the end of the markdown`,
    },
    help: { type: Boolean, alias: 'h', description: `Show this usage guide.` },
};

export const parseOptions: ParseOptions<IWriteMarkDown> = {
    helpArg: 'help',
    loadFromFileArg: 'configFile',
    loadFromFileJsonPathArg: 'jsonPath',
    baseCommand: `write-markdown`,
    optionsHeaderLevel: 3,
    defaultSectionHeaderLevel: 3,
    optionsHeaderText: `write-markdown cli options`,
    headerContentSections: [
        {
            header: 'Markdown Generation',
            headerLevel: 2,
            content: `A markdown version of the usage guide can be generated and inserted into an existing markdown document.
Markers in the document describe where the content should be inserted, existing content betweeen the markers is overwritten.`,
        },
        {
            content: `{highlight write-markdown -m README.MD -j usageGuideConstants.js}`,
        },
    ],
    footerContentSections: [
        {
            header: 'Default Replacement Markers',
            content: `replaceBelow defaults to:
{code '${replaceBelowDefault}'}
replaceAbove defaults to:
{code '${replaceAboveDefault}'}
insertCodeBelow defaults to:
{code '${insertCodeBelowDefault}'}
insertCodeAbove defaults to:
{code '${insertCodeAboveDefault}'}
copyCodeBelow defaults to:
{code '${copyCodeBelowDefault}'}
copyCodeAbove defaults to:
{code '${copyCodeAboveDefault}'}`,
        },
    ],
};

export const usageGuideInfo: UsageGuideConfig<IWriteMarkDown> = {
    arguments: argumentConfig,
    parseOptions,
};
