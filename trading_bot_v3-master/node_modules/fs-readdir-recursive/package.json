{"name": "fs-readdir-recursive", "description": "Recursively read a directory", "version": "1.1.0", "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "devDependencies": {"istanbul": "0", "mocha": "3", "should": "*"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "twitter": "https://twitter.com/jongleberry"}, "repository": "fs-utils/fs-readdir-recursive", "files": ["index.js"], "license": "MIT"}