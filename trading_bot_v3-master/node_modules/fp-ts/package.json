{"name": "fp-ts", "version": "1.19.3", "description": "Functional programming in TypeScript", "files": ["lib", "es6", "rules"], "main": "lib/index.js", "module": "es6/index.js", "typings": "lib/index.d.ts", "sideEffects": false, "scripts": {"lint": "tslint -p tsconfig.tslint.json src/**/*.ts test/**/*.ts", "jest-clear-cache": "jest --clear<PERSON>ache", "jest": "jest --ci", "prettier": "prettier --list-different \"./{src,test,examples,tutorial}/**/*.ts\"", "fix-prettier": "prettier --write \"./{src,test,examples,tutorial}/**/*.ts\"", "test": "npm run lint && npm run prettier && npm run dtslint && npm run jest-clear-cache && npm run jest && npm run docs", "clean": "rimraf lib/* es6/*", "build": "npm run clean && tsc && tsc -p tsconfig.es6.json", "prepublish": "npm run build", "doctoc": "doctoc README.md docs/introduction/code-conventions.md --title \"**Table of contents**\"", "mocha": "mocha -r ts-node/register test/*.ts", "dtslint": "npm run build && dtslint dtslint", "docs": "docs-ts"}, "repository": {"type": "git", "url": "https://github.com/gcanti/fp-ts.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/gcanti/fp-ts/issues"}, "homepage": "https://github.com/gcanti/fp-ts", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/doctrine": "0.0.3", "@types/glob": "^7.1.1", "@types/jest": "22.2.2", "@types/node": "^10.12.21", "@types/prettier": "1.10.0", "benchmark": "2.1.4", "chalk": "2.2.0", "docs-ts": "0.0.3", "doctoc": "^1.4.0", "doctrine": "2.0.0", "dtslint": "github:gcanti/dtslint", "fast-check": "^1.12.1", "glob": "^7.1.3", "jest": "^23.6.0", "mocha": "^5.2.0", "prettier": "^1.18.2", "rimraf": "2.6.2", "ts-jest": "^24.0.0", "ts-node": "^8.0.2", "ts-simple-ast": "17.1.1", "tslint": "5.11.0", "tslint-config-standard": "8.0.1", "typescript": "^3.5.2"}, "tags": ["typescript", "algebraic-data-types", "functional-programming"], "keywords": ["typescript", "algebraic-data-types", "functional-programming"], "dependencies": {}}