{"name": "sc-istanbul", "version": "0.4.6", "description": "Istanbul V0 used by solidity-coverage for its report-api", "keywords": ["coverage", "code coverage", "JS code coverage", "JS coverage"], "author": "<PERSON><PERSON> <kananthm<PERSON>-<EMAIL>>", "license": "BSD-3-<PERSON><PERSON>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Dav <PERSON> <<EMAIL>>", "nowamasa <<EMAIL>>", "<PERSON> @millermedeiros <<EMAIL>>", "<PERSON> @unindented <<EMAIL>>", "<PERSON> @mathiasbynens <<EMAIL>>", "<PERSON> @nbrownus <<EMAIL>>", "<PERSON> @existentialism <<EMAIL>>", "<PERSON> @jrgm", "<PERSON><PERSON> @kami <<EMAIL>>", "<PERSON> @jhan<PERSON> <<EMAIL>>", "Vo<PERSON><PERSON> @vojtajina <<EMAIL>>", "<PERSON> @runk <<EMAIL>>", "<PERSON> @chrisgladd", "<PERSON> <<EMAIL>>", "porneL @pornel <<EMAIL>>", "@asifrc", "Ger<PERSON><PERSON> @gergelyke", "@bixdeng", "@mpderbec", "@jxiaodev", "Arp<PERSON> Bo<PERSON> @Swatinem <<EMAIL>>", "<PERSON><PERSON> @ariya", "@markyen", "<PERSON> @samccone <<EMAIL>>", "<PERSON> @jason0x43", "@smikes", "<PERSON><PERSON><PERSON> @yasyf <<EMAIL>>", "<PERSON><PERSON><PERSON> @piuccio <<EMAIL>>", "<PERSON> @ryan-roemer <<EMAIL>>", "<PERSON> @dougwilson", "<PERSON> @gustavnikolaj <<EMAIL>>", "<PERSON> @denis-sokolov <<EMAIL>>", "<PERSON><PERSON> @ymainier", "<PERSON><PERSON> @dead-horse <<EMAIL>>", "<PERSON> @andrewrk <<EMAIL>>", "Will <PERSON> @wlabranche <<EMAIL>>", "<PERSON><PERSON> @math-nao <<EMAIL>>", "<PERSON> @ronkorving", "<PERSON>-<PERSON> @robatron <rob.mc<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> @booleangate", "<PERSON> @juangabreil <<EMAIL>>", "<PERSON> @dragn <d<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> @tonylukasavage <<EMAIL>>", "<PERSON> @nexus-uw", "Dominykas Blyžė @dominykas", "<PERSON> @sethpollack", "<PERSON> @bcoe <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> @popomore <<EMAIL>>", "<PERSON><PERSON> @rode<PERSON>hsiao", "<PERSON><PERSON> @nkt", "<PERSON> @alexdunphy <<EMAIL>>", "<PERSON><PERSON> @arty-name <<EMAIL>>", "<PERSON><PERSON><PERSON> @aryelu", "@sterlinghw", "Gord <PERSON> <<EMAIL>>", "<PERSON> @tmcw <<EMAIL>>", "<PERSON><PERSON> @kitsonk", "@asa-git", "@RoCat", "<PERSON> @iphands <<EMAIL>>", "<PERSON> @pegurnee", "<PERSON> @kpdecker <<EMAIL>>", "isaacs @isaacs <<EMAIL>>", "<PERSON> @steve-gray", "<PERSON><PERSON><PERSON> @pra85 <<EMAIL>>", "<PERSON> @abejfehr <<EMAIL>>", "<PERSON> @doowb <<EMAIL>>", "@Victorystick", "@inversion", "@JamesMGreene", "@ChALkeR", "@graingert"], "scripts": {"pretest": "jshint index.js lib/ test/ && ./download-escodegen-browser.sh", "test": "node --harmony test/run.js", "posttest": "node ./lib/cli.js check-coverage --statements 95 --branches 80", "docs": "npm install yuidocjs && node node_modules/yuidocjs/lib/cli.js ."}, "bin": {"istanbul": "lib/cli.js"}, "files": ["index.js", "lib/"], "repository": {"type": "git", "url": "git://github.com/gotwarlost/istanbul.git"}, "dependencies": {"abbrev": "1.0.x", "async": "1.x", "escodegen": "1.8.x", "esprima": "2.7.x", "glob": "^5.0.15", "handlebars": "^4.0.1", "js-yaml": "3.x", "mkdirp": "0.5.x", "nopt": "3.x", "once": "1.x", "resolve": "1.1.x", "supports-color": "^3.1.0", "which": "^1.1.1", "wordwrap": "^1.0.0"}, "devDependencies": {"coveralls": "2.x", "jshint": "^2.8.0", "nodeunit": "0.9.x", "requirejs": "2.x", "rimraf": "^2.4.3"}}