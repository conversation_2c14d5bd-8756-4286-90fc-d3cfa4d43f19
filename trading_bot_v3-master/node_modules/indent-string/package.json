{"name": "indent-string", "version": "4.0.0", "description": "Indent each line in a string", "license": "MIT", "repository": "sindresorhus/indent-string", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["indent", "string", "pad", "align", "line", "text", "each", "every"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}