// Simple deployment script for Arbitrage contract
require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  console.log("Deploying Arbitrage contract...");
  
  const Arbitrage = await ethers.getContractFactory("Arbitrage");
  const arbitrage = await Arbitrage.deploy();
  
  await arbitrage.waitForDeployment();
  const address = await arbitrage.getAddress();
  
  console.log(`Arbitrage deployed to: ${address}`);
  console.log("Update this address in your config.json file under PROJECT_SETTINGS.ARBITRAGE_ADDRESS");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });